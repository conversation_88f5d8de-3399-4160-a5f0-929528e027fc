<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Appointment extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'client_name',
        'company_id',
        'order_id',
        'service_id',
        'company_professional_id',
        'company_user_id',
        'status',
        'title',
        'start_date',
        'end_date',
        'original_date',
        'started_at',
        'finished_at',
        'is_recurrent',
        'recursive_value'
    ];

    protected $casts = [
        'start_date' => 'datetime',
        'end_date' => 'datetime',
        'original_date' => 'datetime',
        'is_recurrent' => 'boolean',
    ];

    public function professional()
    {
        return $this->belongsTo(CompanyProfessional::class, 'company_professional_id');
    }

    public function service()
    {
        return $this->belongsTo(Service::class, 'service_id');
    }
    public function order()
    {
        return $this->belongsTo(Order::class, 'order_id');
    }
    public function client()
    {
        return $this->belongsTo(CompanyUser::class, 'company_user_id');
    }

    public function recurrentPause()
    {
        return $this->hasOne(RecurrentPause::class, 'created_from_appointment_id');
    }
}
