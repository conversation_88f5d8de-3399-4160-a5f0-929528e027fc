<template>
  <div>
    <div class="container-table pt-1">
      <div class="container-body flex flex-col">
        <table-base
          title="Agendamentos"
          :columns="columns"
          v-model:per_page="per_page"
          v-model:page="page"
          :rows="orders"
          class="flex-1"
          hide-edit
          hide-delete
          :total-items="totalItems"
          no-create
          :loading="loading"
          :actions="[{ name: 'Visualizar', action: handleView }]"
        >
          <template #filter>
            <div class="flex md:w-4/6 gap-4 items-center">
              <autocomplete
                v-model="filters.value"
                :options="clients"
                force-open
                :loading="loadingClients"
                label="Paciente"
                option-label="name"
                option-value="user_id"
                get-id
                class="w-full"
                input-classes="w-full"
                label-style="!-mb-2 w-10 ml-2 z-20 text-xs bg-base-100"
              />
              <input-base
                class="hidden sm:flex"
                label-style="!-mb-2 ml-2 z-10 text-xs"
                label="Status"
                v-model="filters.status"
                :options="[
                  { value: 'active', label: 'Pendente' },
                  { value: 'finished', label: 'Finalizado' },
                  { value: 'canceled', label: 'Cancelado' },
                ]"
              />
              <input-base
                class="hidden sm:flex"
                :options="[
                  { value: 'total', label: 'Total' },
                  { value: 'created_at', label: 'Data de criação' },
                ]"
                label-style="!-mb-2 ml-2 z-10 text-xs"
                label="Ordenar por"
                v-model="filters.sort_by"
              />
              <input-base
                class="hidden sm:flex"
                label-style="!-mb-2 ml-2 z-10 text-xs"
                v-model="filters.direction"
                :options="[
                  { value: 'asc', label: 'Crescente' },
                  { value: 'desc', label: 'Decrescente' },
                ]"
                label="Ordem"
              />
              <div class="sm:hidden">
                <div class="text-xs -mt-1">Filtros</div>
                <base-button
                  @click="filterModalOpen = true"
                  size="sm"
                  class="mr-3 btn-circle"
                >
                  <AdjustmentsHorizontalIcon class="w-5" />
                </base-button>
              </div>
            </div>
          </template>

          <template #date="{ row }">
            <td>
              <div
              >
                {{ dayjs(row.appointments[0].start_date).format("DD/MM/YYYY - HH:mm") }}
              </div>
            </td>
          </template>
          <template #status="{ value }">
            <td>
              {{ getStatus(value) }}
            </td>
          </template>
          <template #client="{ row }">
            <td>
              <div>
                {{ row.appointments[0].client_name }}
              </div>
            </td>
          </template>
          <template #additional-actions="{ item }">
            <li @click="handleReschedule(item as Order)">
              <a>Remarcar Pedido</a>
            </li>
            <li @click="handleFinish(item as Order)">
              <a>Finalizar Pedido</a>
            </li>
            <li @click="handleCancel(item as Order)">
              <a>Cancelar Pedido</a>
            </li>
          </template>
        </table-base>
      </div>
    </div>
    <base-dialog title="Visualizar Pedido" v-model="isOpened">
      <div v-if="selectedOrder" class="p-4">
        <div class="grid grid-cols-2 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-700"
              >Paciente</label
            >
            <p class="mt-1">{{ selectedOrder.appointments[0].client_name }}</p>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700"
              >Status</label
            >
            <p class="mt-1">{{ getStatus(selectedOrder.status) }}</p>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700">Total</label>
            <p class="mt-1">{{ selectedOrder.total }}</p>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700">Data</label>
            <p class="mt-1">
              {{ dayjs(selectedOrder.created_at).format("DD/MM/YYYY") }}
            </p>
          </div>
          <div class="col-span-2">
            <label class="block text-sm font-medium text-gray-700"
              >Serviços</label
            >
            <div class="mt-1">
              <div
                v-for="appointment in selectedOrder.appointments"
                :key="appointment.id"
              >
                {{ appointment.title }} - {{ appointment.total }}
              </div>
            </div>
          </div>
          <div
            v-if="selectedOrder.appointments[0].associated_products?.length"
            class="col-span-2"
          >
            <label class="block text-sm font-medium text-gray-700"
              >Produtos</label
            >
            <div class="mt-1">
              <div
                v-for="product in selectedOrder.appointments[0]
                  .associated_products"
                :key="product.id"
              >
                {{ product.name }} - {{ product.quantity }}x -
                {{ product.total }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </base-dialog>
    <base-dialog title="Filtros" v-model="filterModalOpen">
      <div class="flex flex-col gap-3">
        <input-base
          :options="[
            { value: 'quantity', label: 'Estoque' },
            { value: 'name', label: 'Nome' },
            { value: 'base_price', label: 'Preço Base' },
          ]"
          label-style="!-mb-2 ml-2 z-10 text-xs"
          label="Ordenar por"
          v-model="filters.sort_by"
        />
        <input-base
          label-style="!-mb-2 ml-2 z-10 text-xs"
          v-model="filters.direction"
          :options="[
            { value: 'asc', label: 'Crescente' },
            { value: 'desc', label: 'Decrescente' },
          ]"
          label="Ordem"
        />
        <base-button @click="handleFilter" class="w-full">Buscar</base-button>
      </div>
    </base-dialog>
    <base-dialog title="Adicionar Estoque" v-model="stockModalOpen">
      <ProductsMovementRegisterForm
        v-if="stockModalOpen"
        @submit="handleStockSubmit"
        :product="selectedProduct"
        :loading="loading"
      />
    </base-dialog>

    <!-- Finish Order Confirmation Dialog -->
    <base-dialog title="Confirmar Finalização" v-model="finishDialogOpen">
      <div class="p-4">
        <p class="mb-4">Tem certeza que deseja finalizar este pedido?</p>
        <div class="flex gap-2 justify-end">
          <button @click="finishDialogOpen = false" class="btn btn-ghost">
            Cancelar
          </button>
          <button @click="confirmFinish" :disabled="loading" class="btn btn-primary">
            <span v-if="loading" class="loading loading-spinner loading-sm"></span>
            Finalizar
          </button>
        </div>
      </div>
    </base-dialog>

    <!-- Cancel Order Confirmation Dialog -->
    <base-dialog title="Confirmar Cancelamento" v-model="cancelDialogOpen">
      <div class="p-4">
        <p class="mb-4">Tem certeza que deseja cancelar este pedido?</p>
        <div class="flex gap-2 justify-end">
          <button @click="cancelDialogOpen = false" class="btn btn-ghost">
            Não
          </button>
          <button @click="confirmCancel" :disabled="loading" class="btn btn-error">
            <span v-if="loading" class="loading loading-spinner loading-sm"></span>
            Sim, Cancelar
          </button>
        </div>
      </div>
    </base-dialog>

    <!-- Reschedule Order Dialog -->
    <base-dialog title="Remarcar Agendamento" v-model="rescheduleDialogOpen">
      <div class="flex flex-col gap-3 p-4">
        <input-base
          v-model="newDate"
          label="Nova Data"
          type="date"
          input-classes="w-full"
        />
        <autocomplete
          v-model="newHour"
          label="Novo Horário"
          force-open
          mask="##:##"
          :loading="loadingSlots"
          :options="slots"
          option-label="hour"
          option-value="hour"
          input-classes="w-full"
          :create-register="false"
          :disabled="!newDate"
        />
        <div class="flex gap-2 justify-end mt-4">
          <button @click="rescheduleDialogOpen = false" class="btn btn-ghost">
            Cancelar
          </button>
          <button @click="confirmReschedule" :disabled="!newDate || !newHour || rescheduleLoading" class="btn btn-primary">
            <span v-if="rescheduleLoading" class="loading loading-spinner loading-sm"></span>
            Remarcar
          </button>
        </div>
      </div>
    </base-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from "vue";
import { useToast } from "vue-toast-notification";
import dayjs from "dayjs";
import { api } from "~/server/api";
import { AdjustmentsHorizontalIcon } from "@heroicons/vue/24/solid";

interface Order {
  id: number;
  company_user_id: number;
  status: string;
  subtotal: string;
  discount: string;
  total: string;
  finished_at: string;
  created_at: string;
  updated_at: string;
  appointments: {
    id: number;
    order_id: number;
    company_professional_id: number;
    service_id: number;
    discount: string;
    total: string;
    status: string;
    created_at: string;
    updated_at: string;
    client_name: string;
    title: string;
    associated_products: {
      id: number;
      order_item_id: number;
      appointment_id: number;
      product_id: number;
      product_variant_id: number | null;
      name: string;
      quantity: number;
      total: string;
      created_at: string;
      updated_at: string;
      title: string | null;
      status: string;
    }[];
  }[];
}

interface ClientData {
  id: number;
  user: {
    id: number;
    name: string;
    email: string;
  };
}
const totalItems = ref(0);
const clients = ref<Client[]>([]);
const loadingClients = ref(false);
const filterModalOpen = ref();
const stockModalOpen = ref(false);
const orders = ref<Order[]>([]);
const selectedProduct = ref<Product>();
const selectedOrder = ref<Order>();
const loading = ref(false);
const isOpened = ref(false);
const finishDialogOpen = ref(false);
const cancelDialogOpen = ref(false);
const rescheduleDialogOpen = ref(false);
const orderToFinish = ref<Order>();
const orderToCancel = ref<Order>();
const orderToReschedule = ref<Order>();
const rescheduleLoading = ref(false);
const loadingSlots = ref(false);
const newDate = ref("");
const newHour = ref("");
const slots = ref([]);
const toast = useToast();
const per_page = ref(10);
const page = ref(1);
const columns = [
  { label: "Paciente", key: "client" },
  // { label: "Total", key: "total", type: "currency" },
  { label: "Data", key: "date", sm: true },
  { label: "Status", key: "status" },
];

const filters = ref({
  value: "",
  status: "",
  sort_by: "created_at",
  direction: "desc",
});

function handleFilter() {
  filterModalOpen.value = false;
  getData();
}

function handleView(order: Order) {
  selectedOrder.value = order;
  isOpened.value = true;
}

function handleFinish(order: Order) {
  orderToFinish.value = order;
  finishDialogOpen.value = true;
}

function handleCancel(order: Order) {
  orderToCancel.value = order;
  cancelDialogOpen.value = true;
}

async function confirmFinish() {
  if (!orderToFinish.value) return;

  try {
    loading.value = true;
    await api.post(`/finish-order/${orderToFinish.value.id}`, {
      status: "finished",
    });
    toast.success("Pedido finalizado com sucesso!");
    finishDialogOpen.value = false;
    await getData();
  } catch (err) {
    console.error(err);
    toast.error("Erro ao finalizar pedido!");
  } finally {
    loading.value = false;
  }
}

async function confirmCancel() {
  if (!orderToCancel.value) return;

  try {
    loading.value = true;
    await api.post(`/finish-order/${orderToCancel.value.id}`, {
      status: "canceled",
    });
    toast.success("Pedido cancelado com sucesso!");
    cancelDialogOpen.value = false;
    await getData();
  } catch (err) {
    console.error(err);
    toast.error("Erro ao cancelar pedido!");
  } finally {
    loading.value = false;
  }
}

function handleReschedule(order: Order) {
  orderToReschedule.value = order;
  // Reset form fields when opening the dialog
  newDate.value = "";
  newHour.value = "";
  slots.value = [];
  rescheduleDialogOpen.value = true;
}

async function confirmReschedule() {
  if (!orderToReschedule.value || !newDate.value || !newHour.value) return;

  // Get the first appointment from the order
  const appointmentId = orderToReschedule.value.appointments?.[0]?.id;
  if (!appointmentId) {
    toast.error("Nenhum agendamento encontrado para este pedido!");
    return;
  }

  try {
    rescheduleLoading.value = true;
    await api.post(`/reschedule-appointment/${appointmentId}`, {
      date: newDate.value,
      hour: newHour.value,
    });
    toast.success("Agendamento remarcado com sucesso!");
    rescheduleDialogOpen.value = false;
    newDate.value = "";
    newHour.value = "";
    slots.value = [];
    await getData();
  } catch (err) {
    console.error(err);
    toast.error("Erro ao remarcar agendamento!");
  } finally {
    rescheduleLoading.value = false;
  }
}

// Watch for date changes to fetch available slots
watch([newDate], async () => {
  if (newDate.value && orderToReschedule.value?.appointments?.[0]?.service_id) {
    // Reset hour when date changes
    newHour.value = "";
    loadingSlots.value = true;
    try {
      const { data } = await api("/slots", {
        params: {
          date: newDate.value,
          serviceId: orderToReschedule.value.appointments[0].service_id,
        },
      });
      const today = dayjs().valueOf();
      const hours = data.filter((slot: { time: string }) => {
        const [hour, minute] = slot.time.split(":");
        const slotTime = dayjs(newDate.value).hour(parseInt(hour)).minute(parseInt(minute)).valueOf();
        return slotTime > today;
      }).map((slot: { time: string }) => ({ hour: slot.time }));
      slots.value = hours;
    } catch (error) {
      console.error("Error fetching slots:", error);
      slots.value = [];
    } finally {
      loadingSlots.value = false;
    }
  } else {
    // Clear slots if no date or service
    slots.value = [];
    newHour.value = "";
  }
});

// Watch for dialog close to reset form
watch(rescheduleDialogOpen, (isOpen) => {
  if (!isOpen) {
    // Reset form when dialog is closed
    newDate.value = "";
    newHour.value = "";
    slots.value = [];
    orderToReschedule.value = undefined;
  }
});

const debouncedFetch = () => getData();

async function getData() {
  try {
    loading.value = true;
    const { data } = await api("/company-orders", {
      params: {
        value: filters.value.value,
        status: filters.value.status,
        sort_by: filters.value.sort_by,
        direction: filters.value.direction,
      },
    });
    orders.value = data.data;
    totalItems.value = data.total;
  } catch (err) {
    console.error(err);
    toast.error("Erro na requisição!");
  } finally {
    loading.value = false;
  }
}

function getStatus(status: string) {
  if (status === "active") return "Pendente";
  if (status === "finished") return "Finalizado";
  if (status === "canceled") return "Cancelado";
}

watch(
  [filters],
  ([filtersVal]) => {
    if (filterModalOpen.value) return;
    if (filtersVal.value) {
      debouncedFetch();
    } else {
      getData();
    }
  },
  { deep: true }
);

onMounted(async () => {
  Promise.all([getData(), getClients()]);
});

async function getClients() {
  loadingClients.value = true;
  const { data } = await api("/clients");
  clients.value = data.data.map((client: ClientData) => {
    return { ...client.user, user_id: client.id };
  });
  loadingClients.value = false;
}
</script>
